"use client";

import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";

// 声明全局QuickService类型
declare global {
  interface Window {
    QuickService: any;
  }
}

interface CustomerServiceButtonProps {
  variant?: 'floating' | 'nav';
  className?: string;
}

// 用户信息类型
interface UserInfo {
  uid: number;
  username: string;
  gameId?: string;
  serverId?: string;
  roleId?: string;
  roleName?: string;
}

export default function CustomerServiceButton({
  variant = 'floating',
  className = ''
}: CustomerServiceButtonProps) {
  const t = useTranslations();
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  // 检查用户登录状态
  useEffect(() => {
    const checkUserLogin = () => {
      // 首先检查全局用户信息
      const globalUser = localStorage.getItem('globalUser');
      if (globalUser) {
        try {
          const globalUserData = JSON.parse(globalUser);
          const loginTime = globalUserData.loginTime || 0;
          const currentTime = Date.now();
          const twentyFourHours = 24 * 60 * 60 * 1000;

          // 检查登录是否在24小时内
          if (currentTime - loginTime < twentyFourHours && globalUserData.uid) {
            // 尝试获取当前页面的游戏特定信息
            const currentPath = window.location.pathname;
            const gameIdMatch = currentPath.match(/\/games\/([^\/]+)/);

            if (gameIdMatch) {
              const gameId = gameIdMatch[1];
              const gameSpecificUser = localStorage.getItem(`gameUser_${gameId}`);

              if (gameSpecificUser) {
                try {
                  const gameUserData = JSON.parse(gameSpecificUser);
                  setUserInfo(gameUserData);
                  return;
                } catch (e) {
                  console.error('解析游戏用户信息失败:', e);
                }
              }
            }

            // 如果没有游戏特定信息，使用全局信息
            setUserInfo(globalUserData);
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    };

    checkUserLogin();

    // 监听localStorage变化
    const handleStorageChange = () => {
      checkUserLogin();
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);







  // 手动刷新用户信息
  const refreshUserInfo = () => {
    const currentPath = window.location.pathname;
    const gameIdMatch = currentPath.match(/\/games\/([^\/]+)/);

    if (gameIdMatch) {
      const gameId = gameIdMatch[1];
      const gameSpecificUser = localStorage.getItem(`gameUser_${gameId}`);

      if (gameSpecificUser) {
        try {
          const gameUserData = JSON.parse(gameSpecificUser);
          console.log('刷新用户信息 - 游戏特定数据:', gameUserData);
          setUserInfo(gameUserData);
          return gameUserData;
        } catch (e) {
          console.error('解析游戏用户信息失败:', e);
        }
      }
    }

    // 如果没有游戏特定信息，检查全局信息
    const globalUser = localStorage.getItem('globalUser');
    if (globalUser) {
      try {
        const globalUserData = JSON.parse(globalUser);
        console.log('刷新用户信息 - 全局数据:', globalUserData);
        setUserInfo(globalUserData);
        return globalUserData;
      } catch (e) {
        console.error('解析全局用户信息失败:', e);
      }
    }

    return null;
  };

  // 客服联系函数 - 仅限已登录且选择角色区服的用户
  const handleCustomerService = () => {
    try {
      console.log('正在启动客服功能...');

      // 先刷新一次用户信息，确保获取最新状态
      const latestUserInfo = refreshUserInfo() || userInfo;

      // 检查SDK是否已加载
      if (typeof window === 'undefined' || !window.QuickService) {
        console.error('QuickService SDK 未加载');
        alert('客服功能暂时不可用，请刷新页面重试');
        return;
      }

      // 调试：输出当前用户信息状态
      console.log('客服功能 - 当前用户信息状态:', {
        userInfo: latestUserInfo,
        hasUserInfo: !!latestUserInfo,
        hasUid: !!(latestUserInfo && latestUserInfo.uid),
        hasServerId: !!(latestUserInfo && latestUserInfo.serverId),
        hasRoleId: !!(latestUserInfo && latestUserInfo.roleId),
        serverId: latestUserInfo?.serverId,
        roleId: latestUserInfo?.roleId,
        roleName: latestUserInfo?.roleName
      });

      // 设置用户信息（支持游客和已登录用户）
      let uid, username;

      if (latestUserInfo && latestUserInfo.uid) {
        // 已登录用户
        uid = latestUserInfo.uid.toString();
        username = latestUserInfo.username || `用户${latestUserInfo.uid}`;

        console.log('已登录用户客服信息:', {
          uid: latestUserInfo.uid,
          username: latestUserInfo.username,
          gameId: latestUserInfo.gameId,
          serverId: latestUserInfo.serverId,
          roleId: latestUserInfo.roleId,
          roleName: latestUserInfo.roleName
        });
      } else {
        // 游客用户
        uid = "guest_" + Date.now();
        username = "游客用户";

        console.log('游客用户客服信息:', {
          uid: uid,
          username: username
        });
      }

      // 调试：查看SDK支持的方法
      console.log('QuickService 支持的方法:', Object.getOwnPropertyNames(window.QuickService));

      // 使用SDK获取客服链接，包含完整的用户角色信息
      var service = window.QuickService
        .setAppId('66848c5efe5f9ab1c66eb320e2163634')
        .setLanguageAppId('en-us','83f39576faf7fbb2ea9be607646b9c72')
        .setLanguageAppId('zh-cn','e54ab11632639ee1b4b7805417cb0106')
        .setLanguageAppId('kor','66848c5efe5f9ab1c66eb320e2163634')
        .setUid(uid)
        .setUsername(username)
        .setChannelCode('website')
        .setWidth('800px')
        .setHeight('700px');

      // 尝试添加角色信息（仅对已登录且有角色信息的用户）
      if (latestUserInfo && latestUserInfo.uid && latestUserInfo.serverId && latestUserInfo.roleId) {
        try {
          console.log('尝试设置角色信息...');

          if (typeof service.setUserRoleId === 'function') {
            service = service.setNickNameId(latestUserInfo.roleId);
            console.log('成功设置 roleId:', latestUserInfo.roleId);
          } else {
            console.log('setUserRoleId 方法不存在');
          }

          if (typeof service.setUserRoleName === 'function') {
            service = service.setNickName(latestUserInfo.roleName || '');
            console.log('成功设置 roleName:', latestUserInfo.roleName);
          } else {
            console.log('setUserRoleName 方法不存在');
          }

          if (typeof service.setuserRoleServer === 'function') {
            service = service.setUserBalance(latestUserInfo.serverId);
            console.log('成功设置 serverId:', latestUserInfo.serverId);
          } else {
            console.log('setuserRoleServer 方法不存在');
          }

          // 尝试其他可能的方法名
          if (typeof service.setOtherParams === 'function') {
            service = service.setOtherParams({
              userRoleId: latestUserInfo.roleId,
              userRoleName: latestUserInfo.roleName || '',
              userRoleServer: latestUserInfo.serverId
            });
            console.log('使用 setOtherParams 设置角色信息');
          }
        } catch (methodError) {
          console.log('设置角色信息时出错:', methodError);
        }
      } else {
        console.log('用户未登录或未选择角色区服，跳过角色信息设置');
      }

      // 获取客服链接
      const serviceUrl = service.getServiceUrl();

      console.log('客服链接是:' + serviceUrl);

      if (serviceUrl) {
        // 打开新窗口
        const newWindow = window.open(
          serviceUrl,
          'customerService', // 窗口名称
          'width=800,height=700,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
        );

        if (newWindow) {
          console.log('客服窗口已打开');
          // 聚焦到新窗口
          newWindow.focus();
        } else {
          console.error('无法打开客服窗口，可能被浏览器阻止了弹窗');
          alert('无法打开客服窗口，请检查浏览器是否阻止了弹窗，或者允许本站点的弹窗');
        }
      } else {
        console.error('获取客服链接失败');
        alert('获取客服链接失败，请稍后再试');
      }
    } catch (error) {
      console.error('客服功能调用失败:', error);
      alert('客服功能出现错误，请稍后再试');
    }
  };

  if (variant === 'floating') {
    return (
      <button
        onClick={handleCustomerService}
        className={`fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center group ${className}`}
        style={{ zIndex: 10000 }} // 确保在支付栏之上
        aria-label={t('customerService.contact')}
        title={t('customerService.contact')}
      >
        {/* 客服图标 */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 transition-transform duration-300 group-hover:scale-110"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={2}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        
        {/* 悬浮提示 */}
        <div className="absolute right-full mr-3 px-3 py-2 bg-zinc-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
          {t('customerService.contact')}
          <div className="absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-zinc-800 rotate-45"></div>
        </div>
      </button>
    );
  }

 
  return (
    <button
      onClick={handleCustomerService}
      className={`flex items-center space-x-2 text-sm font-medium text-zinc-400 hover:text-white transition-colors ${className}`}
      aria-label={t('customerService.contact')}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-4 w-4 flex-shrink-0"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        strokeWidth={2}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
        />
      </svg>
      <span className="flex-shrink-0">{t('customerService.title')}</span>
    </button>
  );
}
