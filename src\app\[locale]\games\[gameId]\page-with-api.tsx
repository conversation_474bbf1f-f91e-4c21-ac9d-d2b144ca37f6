"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useTranslations } from "next-intl";
import Layout from "../../../components/Layout";
import ProductCard from "../../../components/ProductCard";
import FloatingPaymentBar from "../../../components/FloatingPaymentBar";
import PaymentModal from "../../../components/PaymentModal";
import { fetchSiteInfo, SiteInfo } from "../../../lib/games/api";
import { useExtractGameData, getCurrencySymbol } from "../../../lib/games/utils";
import { ApiGame } from "../../../lib/games/types";

interface GamePageProps {
  params: {
    gameId: string;
    locale: string;
  };
}

export default function GamePage(props: GamePageProps) {
  const { gameId } = props.params;
  const t = useTranslations();
  
  // 状态管理
  const [apiGameData, setApiGameData] = useState<ApiGame | null>(null);
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [paymentBarHighlight, setPaymentBarHighlight] = useState(false);
  
  // 从API获取游戏数据和站点信息
  useEffect(() => {
    async function loadData() {
      setLoading(true);
      try {
        // 获取指定游戏ID的站点信息和游戏详情
        const siteData = await fetchSiteInfo(gameId);
        
        // 类型检查和转换，确保类型安全
        if ('i18n' in siteData && typeof siteData.id === 'number') {
          // 确认是SiteInfo类型
          setSiteInfo(siteData as SiteInfo);
          
          if (siteData.game) {
            setApiGameData(siteData.game as ApiGame);
            setError(null);
          } else {
            setError("游戏不存在");
          }
        } else {
          // 如果是ApiGame类型，需要转换
          setApiGameData(siteData as ApiGame);
          setError(null);
        }
      } catch (err) {
        setError("加载数据失败");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, [gameId]);
  
  // 根据当前语言提取游戏数据
  const game = useExtractGameData(apiGameData);
  
  // 选中的产品
  const selectedProduct = selectedProductId && game
    ? game.products.find((product) => product.id === selectedProductId)
    : null;
  
  const handleProductSelect = (productId: string) => {
    setSelectedProductId(productId);

    // 触发支付栏高亮效果，提醒用户注意底部支付按钮
    setPaymentBarHighlight(true);
    setTimeout(() => {
      setPaymentBarHighlight(false);
    }, 2000); // 2秒后取消高亮
  };
  
  const handlePaymentClick = () => {
    if (selectedProductId) {
      setIsPaymentModalOpen(true);
    }
  };
  
  // 加载状态
  if (loading) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-zinc-400">加载中...</p>
        </div>
      </Layout>
    );
  }
  
  // 错误状态
  if (error || !game) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <h1 className="text-2xl font-bold text-white mb-4">{error ? '网站异常' : t('ui.gameNotFound')}</h1>
          <p className="text-zinc-400 mb-8">{error ? '服务器响应异常，请稍后再试' : t('ui.gameNotFoundDesc')}</p>
          <Link
            href="/"
            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            {t('ui.backToHome')}
          </Link>
        </div>
      </Layout>
    );
  }
  
  // 获取当前语言的站点标题
  const siteTitle = siteInfo?.i18n?.title[props.params.locale] || '';
  
  return (
    <Layout>
      {siteInfo && siteInfo.i18n && (
        <div className="mb-2 text-zinc-400 text-sm">
          {siteTitle}
        </div>
      )}
      
      <div className="mb-8">
        <Link
          href="/"
          className="inline-flex items-center text-sm font-medium text-indigo-400 hover:text-indigo-300 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
          {t('ui.backToHome')}
        </Link>
      </div>

      <div className="relative h-64 sm:h-80 md:h-96 rounded-2xl overflow-hidden mb-8">
        <Image
          src={game.bannerUrl}
          alt={game.title}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
            {game.title}
          </h1>
          <div className="flex items-center space-x-4">
            <span className="text-sm font-semibold px-2 py-1 rounded-full bg-indigo-500/20 text-indigo-300">
              {game.category}
            </span>
            <span className="text-sm text-zinc-300 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1 text-yellow-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                />
              </svg>
              {game.rating}
            </span>
            <span className="text-sm text-zinc-300 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              {game.playerCount}
            </span>
          </div>
        </div>
      </div>

      <div className="mb-12">
        <h2 className="text-xl font-bold text-white mb-2">{t('ui.gameIntro')}</h2>
        <p className="text-zinc-300">{game.description}</p>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-bold text-white mb-6">{t('ui.selectRechargeItem')}</h2>
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {game.products.map((product) => (
            <ProductCard
              key={product.id}
              id={product.id}
              title={product.title}
              imageUrl={product.imageUrl}
              price={product.price}
             
              originalPrice={product.originalPrice}
              currency={product.currency || game.currency}
              description={product.description}
              onSelect={handleProductSelect}
              selected={selectedProductId === product.id}
            />
          ))}
        </div>
      </div>

      {/* 永久悬浮支付栏 - 始终固定在屏幕底部 */}
      <FloatingPaymentBar highlight={paymentBarHighlight} className="md:hidden">
        <div className="space-y-2">
          {/* 选中商品信息 */}
          {selectedProduct && (
            <div className="text-center">
              <p className="text-xs text-zinc-400">{t('ui.selected')}</p>
              <p className="text-sm font-medium text-white truncate">
                {selectedProduct.title}
              </p>
            </div>
          )}

          {/* 支付按钮 */}
          <button
            onClick={handlePaymentClick}
            disabled={!selectedProduct}
            className={`w-full py-4 px-6 rounded-lg font-medium transition-all duration-200 text-base ${
              selectedProduct
                ? `bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 ${
                    paymentBarHighlight ? 'animate-pulse' : ''
                  }`
                : "bg-zinc-800 text-zinc-500"
            }`}
            style={{
              touchAction: 'manipulation',
              WebkitTapHighlightColor: 'transparent'
            }}
          >
            {selectedProduct
              ? `${t('ui.payNow')}: ${getCurrencySymbol(selectedProduct.currency || game.currency)}${selectedProduct.price}`
              : t('ui.selectProduct')}
          </button>
        </div>
      </FloatingPaymentBar>

      {/* 为固定支付栏预留空间，避免内容被遮挡 */}
      <div className="h-24 md:hidden"></div>

      <div className="hidden md:block mb-16">
        <button
          onClick={handlePaymentClick}
          disabled={!selectedProduct}
          className={`py-3 px-8 rounded-lg font-medium transition-colors ${
            selectedProduct
              ? "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white"
              : "bg-zinc-800 text-zinc-500"
          }`}
        >
          {selectedProduct
            ? `${t('ui.payNow')}: ${getCurrencySymbol(selectedProduct.currency || game.currency)}${selectedProduct.price}`
            : t('ui.selectProduct')}
        </button>
      </div>

      {/* 支付模态框 */}
      {selectedProduct && game && (
        <PaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          product={selectedProduct}
          game={game}
        />
      )}
    </Layout>
  );
}