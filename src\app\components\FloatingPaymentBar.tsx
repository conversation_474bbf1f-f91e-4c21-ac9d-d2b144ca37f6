"use client";

import { ReactNode } from 'react';

interface FloatingPaymentBarProps {
  children: ReactNode;
  highlight?: boolean;
  className?: string;
}

export default function FloatingPaymentBar({
  children,
  highlight = false,
  className = ''
}: FloatingPaymentBarProps) {
  console.log('FloatingPaymentBar 渲染中...', { highlight, className });

  return (
    <div
      data-floating-payment-bar
      className={`
        fixed bottom-0 left-0 right-0 w-full
        bg-red-500/95 backdrop-blur-lg border-t
        shadow-2xl transition-all duration-500
        z-[9999] p-4
        ${highlight
          ? 'border-indigo-400/70 shadow-indigo-500/20 shadow-2xl bg-red-600/98'
          : 'border-zinc-800/50'
        }
        ${className}
      `}
      style={{
        position: 'fixed !important' as any,
        bottom: '0 !important',
        left: '0 !important',
        right: '0 !important',
        width: '100vw !important',
        zIndex: '9999 !important',
        WebkitTransform: 'translateZ(0)',
        transform: 'translateZ(0)',
        willChange: 'transform',
        WebkitBackfaceVisibility: 'hidden',
        backfaceVisibility: 'hidden',
        isolation: 'isolate',
        pointerEvents: 'auto',
        contain: 'layout style paint',
        // 临时使用红色背景便于调试
        backgroundColor: 'rgba(239, 68, 68, 0.95)',
        // iOS Safari 兼容性
        WebkitPosition: 'fixed' as any,
        // 安全区域适配
        paddingBottom: 'calc(1rem + env(safe-area-inset-bottom, 0))',
      }}
      role="toolbar"
      aria-label="支付操作栏"
    >
      <div className="container mx-auto">
        <div className="text-white text-center py-2 mb-2">
          🔴 悬浮支付栏测试 - 如果您看到这个红色条，说明悬浮功能正常
        </div>
        {children}
      </div>
    </div>
  );
}
