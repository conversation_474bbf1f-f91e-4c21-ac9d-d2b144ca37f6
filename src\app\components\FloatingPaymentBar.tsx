"use client";

import { ReactNode } from 'react';

interface FloatingPaymentBarProps {
  children: ReactNode;
  highlight?: boolean;
  className?: string;
}

export default function FloatingPaymentBar({ 
  children, 
  highlight = false, 
  className = '' 
}: FloatingPaymentBarProps) {
  return (
    <div
      data-floating-payment-bar
      className={`
        fixed bottom-0 left-0 right-0 w-full
        bg-zinc-900/95 backdrop-blur-lg border-t
        shadow-2xl transition-all duration-500
        z-[9999] p-4
        ${highlight
          ? 'border-indigo-400/70 shadow-indigo-500/20 shadow-2xl bg-zinc-900/98'
          : 'border-zinc-800/50'
        }
        ${className}
      `}
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        width: '100vw',
        zIndex: 9999,
        WebkitTransform: 'translateZ(0)',
        transform: 'translateZ(0)',
        willChange: 'transform',
        WebkitBackfaceVisibility: 'hidden',
        backfaceVisibility: 'hidden',
        isolation: 'isolate',
        pointerEvents: 'auto',
        contain: 'layout style paint',
        // iOS Safari 兼容性
        WebkitPosition: 'fixed' as any,
        // 安全区域适配
        paddingBottom: 'calc(1rem + env(safe-area-inset-bottom, 0))',
        bottom: 'env(safe-area-inset-bottom, 0)',
      }}
      role="toolbar"
      aria-label="支付操作栏"
    >
      <div className="container mx-auto">
        {children}
      </div>
    </div>
  );
}
